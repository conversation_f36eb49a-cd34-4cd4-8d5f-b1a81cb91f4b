2025-11-12 15:54:54,591 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:54:54,618 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618617Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:54:54,618 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618776Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:20,117 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:20,132 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.132893Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:20,133 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.133018Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:32,886 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:32,902 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902402Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:32,902 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902525Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:44,650 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:44,664 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664276Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:44,664 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664382Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:57,415 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:57,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428759Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:57,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428854Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:56:12,227 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:56:12,241 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241407Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:56:12,241 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241514Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:25,115 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:25,129 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129737Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:25,129 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129863Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:34,837 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:34,851 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851076Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:34,851 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851174Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:42,712 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:42,725 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725533Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:42,725 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:55,541 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:55,555 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555699Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:55,555 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555805Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:28,158 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:28,171 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171702Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:28,171 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171801Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:38,904 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:38,918 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918317Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:38,918 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918420Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:50,682 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:50,696 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696177Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:50,696 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696285Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:04,532 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:04,546 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546710Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:04,546 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546822Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:19,392 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:19,405 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.405896Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:19,406 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.406004Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,414 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428528Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428623Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,832 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,843 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843453Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,843 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843592Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
