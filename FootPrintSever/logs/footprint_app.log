2025-11-12 15:54:54,591 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:54:54,618 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618617Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:54:54,618 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "timestamp": "2025-11-12T07:54:54.618776Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:20,117 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:20,132 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.132893Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:20,133 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:20", "timestamp_unix": 1762934120, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:20.133018Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:32,886 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:32,902 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902402Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:32,902 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:32", "timestamp_unix": 1762934132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:32.902525Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:44,650 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:44,664 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664276Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:44,664 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:44", "timestamp_unix": 1762934144, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:44.664382Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:55:57,415 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:55:57,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428759Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:55:57,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:55:57", "timestamp_unix": 1762934157, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:55:57.428854Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:56:12,227 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:56:12,241 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241407Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:56:12,241 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:56:12", "timestamp_unix": 1762934172, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:56:12.241514Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:25,115 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:25,129 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129737Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:25,129 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:25", "timestamp_unix": 1762934305, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:25.129863Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:58:34,837 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:58:34,851 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851076Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:58:34,851 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:58:34", "timestamp_unix": 1762934314, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:58:34.851174Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:42,712 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:42,725 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725533Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:42,725 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:42", "timestamp_unix": 1762934382, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:42.725628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 15:59:55,541 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 15:59:55,555 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555699Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 15:59:55,555 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "15:59:55", "timestamp_unix": 1762934395, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T07:59:55.555805Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:28,158 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:28,171 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171702Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:28,171 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:28", "timestamp_unix": 1762934428, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:28.171801Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:38,904 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:38,918 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918317Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:38,918 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:38", "timestamp_unix": 1762934438, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:38.918420Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:00:50,682 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:00:50,696 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696177Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:00:50,696 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:00:50", "timestamp_unix": 1762934450, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:00:50.696285Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:04,532 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:04,546 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546710Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:04,546 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:04", "timestamp_unix": 1762934464, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:04.546822Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:01:19,392 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:01:19,405 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.405896Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:01:19,406 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:01:19", "timestamp_unix": 1762934479, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:01:19.406004Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,414 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,428 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428528Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,428 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.428623Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:14,832 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 16:22:14,843 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843453Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 16:22:14,843 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "16:22:14", "timestamp_unix": 1762935734, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:14.843592Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 16:22:27,652 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "out_trade_no": "FP17629357478a7a8c42", "amount": 1, "notify_url": "http://localhost:8000/webhook/wechatpay", "event": "Creating WeChat Pay APP transaction", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:27.652005Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:288]
2025-11-12 16:22:27,652 DEBUG: {"method": "POST", "url_path": "/v3/pay/transactions/app", "timestamp": "2025-11-12T08:22:27.652421Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "body_length": 261, "sign_str_length": 336, "event": "Signature string constructed", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:84]
2025-11-12 16:22:27,726 DEBUG: {"signature_length": 344, "event": "Signature generated successfully", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:27.726078Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:105]
2025-11-12 16:22:27,726 DEBUG: {"mch_id": "1727996379", "cert_serial": "7A70A0F2B47751DB4D29ECF93109A6995274FA40", "timestamp": "2025-11-12T08:22:27.726223Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "event": "Authorization header built", "logger": "app.services.subscription_order_service", "level": "debug", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "DEBUG", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:143]
2025-11-12 16:22:27,726 INFO: {"method": "POST", "url": "https://api.mch.weixin.qq.com/v3/pay/transactions/app", "timestamp": "2025-11-12T08:22:27.726294Z", "nonce_str": "D0E54573023C47D9949A24596741D976", "body_size": 261, "event": "Making WeChat Pay API request", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:27", "timestamp_unix": 1762935747, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:184]
2025-11-12 16:22:28,249 INFO: {"status_code": 200, "response_size": 52, "event": "WeChat Pay API response received", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.249181Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:200]
2025-11-12 16:22:28,250 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "out_trade_no": "FP17629357478a7a8c42", "prepay_id": "wx12162228172821b020912f5b104bd80000", "event": "Subscription order created successfully", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.250258Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:299]
2025-11-12 16:22:28,250 INFO: {"prepay_id": "wx12162228172821b020912f5b104bd80000", "timestamp": "2025-11-12T08:22:28.250552Z", "nonce_str": "F6FEEDA1DE2E4E96976E7E533B13EE28", "event": "Client payment parameters generated successfully", "logger": "app.services.subscription_order_service", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "user_id": 10, "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_order_service.py:493]
2025-11-12 16:22:28,250 INFO: {"user_id": 10, "plan_id": 1, "order_id": 75, "event": "Subscription order created successfully", "logger": "app.routes.billing", "level": "info", "request_id": "6d8a67de920e66bf", "method": "POST", "path": "/billing/subscription/order", "remote_addr": "**************", "user_agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22041211AC Build/TP1A.220624.014)", "date": "2025-11-12", "time": "16:22:28", "timestamp_unix": 1762935748, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T08:22:28.250760Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/routes/billing.py:524]
2025-11-12 17:01:19,456 INFO: {"event": "Starting expired subscriptions processing task", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.456388Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:21]
2025-11-12 17:01:19,525 INFO: {"total_found": 0, "processed": 0, "event": "Processed expired subscriptions", "logger": "app.services.subscription_service", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.525115Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/services/subscription_service.py:250]
2025-11-12 17:01:19,525 INFO: {"processed_count": 0, "event": "Expired subscriptions processing completed", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:01:19", "timestamp_unix": 1762938079, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:01:19.525425Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:25]
2025-11-12 17:18:14,338 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:14,342 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:14,353 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353628Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:14,353 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353755Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:14,353 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.353882Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:14,354 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:14", "timestamp_unix": 1762939094, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:14.354032Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:24,242 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:24,253 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:24,256 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.256855Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:24,256 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.256983Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:24,264 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.264332Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:24,264 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:24", "timestamp_unix": 1762939104, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:24.264445Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:52,437 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:52,443 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:18:52,452 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.452194Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:52,452 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.452313Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:18:52,454 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.454161Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:18:52,454 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:18:52", "timestamp_unix": 1762939132, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:18:52.454271Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:20:22,341 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:20:22,346 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 17:20:22,357 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357174Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:20:22,357 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357312Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 17:20:22,357 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357433Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 17:20:22,357 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "17:20:22", "timestamp_unix": 1762939222, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T09:20:22.357579Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 18:16:01,564 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 18:16:01,569 WARNING: Redis connection failed: Error 61 connecting to localhost:6379. Connection refused. [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/extensions.py:39]
2025-11-12 18:16:01,581 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.580991Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 18:16:01,581 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581143Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
2025-11-12 18:16:01,581 INFO: {"event": "Initializing scheduler tasks", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581498Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:119]
2025-11-12 18:16:01,581 INFO: {"event": "Scheduler tasks initialized successfully", "logger": "app.tasks.subscription_tasks", "level": "info", "date": "2025-11-12", "time": "18:16:01", "timestamp_unix": 1762942561, "log_level": "INFO", "application": "footprint_subscription", "version": "1.0.0", "timestamp": "2025-11-12T10:16:01.581642Z"} [in /Users/<USER>/AndroidStudioProjects/FootPrintSever/app/tasks/subscription_tasks.py:124]
