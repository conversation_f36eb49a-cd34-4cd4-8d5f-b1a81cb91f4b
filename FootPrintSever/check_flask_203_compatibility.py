#!/usr/bin/env python3
"""
Flask 2.0.3 兼容性检查脚本
检查当前项目是否可以降级到 Flask 2.0.3
"""
import sys
import subprocess
import pkg_resources
from packaging import version

def check_package_compatibility():
    """检查包兼容性"""
    print("🔍 检查 Flask 2.0.3 兼容性...")
    print("=" * 50)
    
    # 当前项目的依赖要求
    requirements = {
        'Flask': '2.0.3',
        'Flask-SQLAlchemy': '3.0.5',
        'Flask-Migrate': '4.0.5',
        'Flask-JWT-Extended': '4.5.3',
        'Flask-CORS': '4.0.0',
        'flask-talisman': '1.1.0',
        'Flask-Limiter': '3.5.0',
        'Flask-APScheduler': '1.13.0',
    }
    
    # 已知的兼容性要求
    compatibility_matrix = {
        'Flask-SQLAlchemy': {
            '3.0.5': {'min_flask': '2.2.0', 'status': '❌ 不兼容'},
            '3.0.x': {'min_flask': '2.2.0', 'status': '❌ 不兼容'},
            '2.5.1': {'min_flask': '2.0.0', 'status': '✅ 兼容'},
        },
        'Flask-Migrate': {
            '4.0.5': {'min_flask': '2.2.0', 'status': '❌ 不兼容'},
            '3.1.0': {'min_flask': '2.0.0', 'status': '✅ 兼容'},
        },
        'Flask-JWT-Extended': {
            '4.5.3': {'min_flask': '2.0.0', 'status': '✅ 兼容'},
        },
        'Flask-CORS': {
            '4.0.0': {'min_flask': '2.0.0', 'status': '✅ 兼容'},
        },
        'flask-talisman': {
            '1.1.0': {'min_flask': '1.0.0', 'status': '✅ 兼容'},
        },
        'Flask-Limiter': {
            '3.5.0': {'min_flask': '2.0.0', 'status': '✅ 兼容'},
        },
        'Flask-APScheduler': {
            '1.13.0': {'min_flask': '1.0.0', 'status': '✅ 兼容'},
        }
    }
    
    print("📦 扩展兼容性检查:")
    print("-" * 30)
    
    compatible_count = 0
    total_count = len(requirements) - 1  # 排除 Flask 本身
    
    for package, current_version in requirements.items():
        if package == 'Flask':
            continue
            
        if package in compatibility_matrix:
            compat_info = compatibility_matrix[package].get(current_version, {})
            min_flask = compat_info.get('min_flask', 'Unknown')
            status = compat_info.get('status', '⚠️ 未知')
            
            print(f"{package} {current_version}")
            print(f"  最低 Flask 要求: {min_flask}")
            print(f"  与 Flask 2.0.3 兼容性: {status}")
            
            if status.startswith('✅'):
                compatible_count += 1
        else:
            print(f"{package} {current_version}: ⚠️ 需要手动检查")
        
        print()
    
    print("📊 兼容性总结:")
    print(f"兼容: {compatible_count}/{total_count}")
    print(f"兼容率: {compatible_count/total_count*100:.1f}%")
    
    return compatible_count == total_count

def create_flask_203_requirements():
    """创建 Flask 2.0.3 兼容的 requirements.txt"""
    print("\n🔧 创建 Flask 2.0.3 兼容的依赖文件...")
    
    compatible_requirements = """# Flask 2.0.3 兼容版本
# 核心 Flask
Flask==2.0.3

# 数据库 (降级到兼容版本)
Flask-SQLAlchemy==2.5.1
Flask-Migrate==3.1.0

# 认证和安全 (保持当前版本)
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
flask-talisman==1.1.0

# 限流和调度 (保持当前版本)
Flask-Limiter==3.5.0
Flask-APScheduler==1.13.0

# 数据库驱动
PyMySQL==1.1.0
cryptography==41.0.7

# HTTP 客户端
httpx==0.25.0

# 微信支付 SDK
wechatpayv3==1.2.12

# Redis
redis==5.0.1

# 任务调度
APScheduler==3.10.4

# 配置和环境
python-dotenv==1.0.0

# 日志
structlog==23.2.0

# 时区支持
pytz==2023.3

# 开发和测试
pytest==7.4.3
pytest-flask==1.3.0
pytest-mock==3.12.0

# 生产服务器
gunicorn==21.2.0
"""
    
    with open('requirements_flask203.txt', 'w') as f:
        f.write(compatible_requirements)
    
    print("✅ 已创建 requirements_flask203.txt")
    print("⚠️  注意: Flask-SQLAlchemy 和 Flask-Migrate 需要降级")

def check_code_compatibility():
    """检查代码兼容性"""
    print("\n🔍 检查代码兼容性...")
    print("-" * 30)
    
    # Flask 2.0.3 vs 2.3.3 的主要差异
    differences = [
        {
            'feature': 'async/await 支持',
            'flask_203': '基础支持',
            'flask_233': '完整支持',
            'impact': '⚠️ 如果使用了高级异步特性可能有问题'
        },
        {
            'feature': '类型提示',
            'flask_203': '部分支持',
            'flask_233': '完整支持',
            'impact': '✅ 不影响运行时'
        },
        {
            'feature': 'Werkzeug 版本',
            'flask_203': '2.0.x',
            'flask_233': '2.3.x',
            'impact': '⚠️ 可能影响某些底层功能'
        },
        {
            'feature': '安全特性',
            'flask_203': '基础',
            'flask_233': '增强',
            'impact': '⚠️ 安全性可能降低'
        }
    ]
    
    for diff in differences:
        print(f"特性: {diff['feature']}")
        print(f"  Flask 2.0.3: {diff['flask_203']}")
        print(f"  Flask 2.3.3: {diff['flask_233']}")
        print(f"  影响: {diff['impact']}")
        print()

def main():
    """主函数"""
    print("🔄 Flask 2.0.3 降级兼容性分析")
    print("=" * 60)
    
    # 检查包兼容性
    is_compatible = check_package_compatibility()
    
    # 检查代码兼容性
    check_code_compatibility()
    
    # 创建兼容的 requirements 文件
    create_flask_203_requirements()
    
    print("\n🎯 总结和建议:")
    print("=" * 30)
    
    if is_compatible:
        print("✅ 理论上可以降级到 Flask 2.0.3")
        print("⚠️  但需要注意以下问题:")
        print("   1. Flask-SQLAlchemy 需要从 3.0.5 降级到 2.5.1")
        print("   2. Flask-Migrate 需要从 4.0.5 降级到 3.1.0")
        print("   3. 可能失去一些新的安全特性")
        print("   4. 需要充分测试所有功能")
    else:
        print("❌ 不建议降级到 Flask 2.0.3")
        print("   主要扩展不兼容，需要大量修改")
    
    print("\n📋 降级步骤 (如果决定降级):")
    print("1. 备份当前环境")
    print("2. 使用 requirements_flask203.txt 重新安装依赖")
    print("3. 测试所有功能")
    print("4. 检查数据库迁移兼容性")
    print("5. 进行完整的回归测试")

if __name__ == '__main__':
    main()
