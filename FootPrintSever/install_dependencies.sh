#!/bin/bash
"""
阿里云服务器依赖安装脚本
解决镜像源版本不全的问题
"""

set -e  # 遇到错误立即退出

echo "🚀 FootPrint 服务依赖安装脚本"
echo "适用于阿里云服务器环境"
echo "=" * 50

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local message=$1
    local status=$2
    
    case $status in
        "ok")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_status "当前为 root 用户" "ok"
    else
        print_status "建议使用 root 用户运行此脚本" "warning"
    fi
}

# 备份当前 pip 配置
backup_pip_config() {
    print_status "备份当前 pip 配置..." "info"
    
    if [ -f ~/.pip/pip.conf ]; then
        cp ~/.pip/pip.conf ~/.pip/pip.conf.backup.$(date +%Y%m%d_%H%M%S)
        print_status "已备份原配置文件" "ok"
    fi
}

# 配置 pip 镜像源
configure_pip_sources() {
    print_status "配置 pip 镜像源..." "info"
    
    # 创建 pip 配置目录
    mkdir -p ~/.pip
    
    # 创建配置文件
    cat > ~/.pip/pip.conf << 'EOF'
[global]
# 主源：清华大学镜像（通常最新）
index-url = https://pypi.tuna.tsinghua.edu.cn/simple/

# 备用源
extra-index-url = 
    https://pypi.org/simple/
    https://pypi.mirrors.ustc.edu.cn/simple/
    https://mirrors.aliyun.com/pypi/simple/

# 信任的主机
trusted-host = 
    pypi.tuna.tsinghua.edu.cn
    pypi.org
    pypi.mirrors.ustc.edu.cn
    mirrors.aliyun.com

# 超时设置
timeout = 120
EOF

    print_status "pip 镜像源配置完成" "ok"
}

# 测试镜像源
test_pip_sources() {
    print_status "测试镜像源可用性..." "info"
    
    # 测试能否获取 Flask 2.3.3
    if pip index versions Flask | grep -q "2.3.3"; then
        print_status "Flask 2.3.3 可用" "ok"
        return 0
    else
        print_status "Flask 2.3.3 不可用，尝试其他源..." "warning"
        return 1
    fi
}

# 安装依赖包
install_dependencies() {
    print_status "开始安装项目依赖..." "info"
    
    # 升级 pip
    print_status "升级 pip..." "info"
    python3 -m pip install --upgrade pip
    
    # 尝试安装 Flask 2.3.3
    print_status "尝试安装 Flask 2.3.3..." "info"
    if pip install Flask==2.3.3; then
        print_status "Flask 2.3.3 安装成功" "ok"
        FLASK_VERSION="2.3.3"
    else
        print_status "Flask 2.3.3 安装失败，使用 2.0.3" "warning"
        pip install Flask==2.0.3
        FLASK_VERSION="2.0.3"
    fi
    
    # 根据 Flask 版本选择兼容的依赖
    if [ "$FLASK_VERSION" = "2.3.3" ]; then
        print_status "安装 Flask 2.3.3 兼容依赖..." "info"
        pip install -r requirements.txt
    else
        print_status "安装 Flask 2.0.3 兼容依赖..." "info"
        
        # 创建兼容的 requirements
        cat > requirements_compatible.txt << 'EOF'
# Flask 2.0.3 兼容版本
Flask==2.0.3
Flask-SQLAlchemy==2.5.1
Flask-Migrate==3.1.0
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
flask-talisman==1.1.0
Flask-Limiter==3.5.0
Flask-APScheduler==1.13.0

# 数据库
PyMySQL==1.1.0
cryptography==41.0.7

# HTTP 客户端
httpx==0.25.0

# 微信支付 SDK
wechatpayv3==1.2.12

# Redis
redis==5.0.1

# 任务调度
APScheduler==3.10.4

# 配置和环境
python-dotenv==1.0.0

# 日志
structlog==23.2.0

# 时区支持
pytz==2023.3

# 开发和测试
pytest==7.4.3
pytest-flask==1.3.0
pytest-mock==3.12.0

# 生产服务器
gunicorn==21.2.0
EOF
        
        pip install -r requirements_compatible.txt
    fi
    
    print_status "依赖安装完成" "ok"
}

# 验证安装
verify_installation() {
    print_status "验证安装结果..." "info"
    
    # 检查关键包
    python3 -c "
import flask
import flask_sqlalchemy
import pymysql
import redis
print(f'✅ Flask: {flask.__version__}')
print(f'✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}')
print('✅ PyMySQL: OK')
print('✅ Redis: OK')
"
    
    print_status "安装验证完成" "ok"
}

# 创建启动脚本
create_startup_script() {
    print_status "创建阿里云优化启动脚本..." "info"
    
    cat > aliyun_start.py << 'EOF'
#!/usr/bin/env python3
"""
阿里云服务器优化启动脚本
"""
import os
import sys
from dotenv import load_dotenv

# 确保项目根目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 加载环境变量
load_dotenv()

# 只在开发环境加载 .env.local
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

# 清除代理设置
for var in ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']:
    os.environ.pop(var, None)

# 导入应用
from app import create_app
from app.config import config

# 创建应用实例
app = create_app(config[flask_env])

# 宝塔需要的应用变量
application = app

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    
    print(f"🚀 FootPrint 服务启动 (阿里云优化版)")
    print(f"📍 端口: {port}")
    print(f"🔧 环境: {flask_env}")
    
    # 生产环境尝试使用 Gunicorn
    if flask_env == 'production':
        try:
            import gunicorn.app.wsgiapp as wsgi
            print("✅ 使用 Gunicorn 生产服务器")
            
            sys.argv = [
                'gunicorn',
                '--bind', f'0.0.0.0:{port}',
                '--workers', '2',
                '--worker-class', 'sync',
                '--timeout', '120',
                '--keepalive', '5',
                '--max-requests', '1000',
                'aliyun_start:app'
            ]
            wsgi.run()
        except ImportError:
            print("⚠️  Gunicorn 未安装，使用 Flask 开发服务器")
            app.run(host='0.0.0.0', port=port, debug=False)
    else:
        app.run(host='0.0.0.0', port=port, debug=True)
EOF

    chmod +x aliyun_start.py
    print_status "阿里云启动脚本创建完成" "ok"
}

# 主函数
main() {
    echo
    print_status "开始执行安装流程..." "info"
    echo
    
    # 检查用户权限
    check_root
    
    # 备份配置
    backup_pip_config
    
    # 配置镜像源
    configure_pip_sources
    
    # 测试镜像源
    if ! test_pip_sources; then
        print_status "使用备用安装策略..." "warning"
    fi
    
    # 安装依赖
    install_dependencies
    
    # 验证安装
    verify_installation
    
    # 创建启动脚本
    create_startup_script
    
    echo
    print_status "安装完成！" "ok"
    echo
    print_status "使用方法:" "info"
    echo "  开发环境: python3 aliyun_start.py"
    echo "  生产环境: FLASK_ENV=production python3 aliyun_start.py"
    echo "  宝塔面板: 使用 aliyun_start.py 作为启动文件"
    echo
}

# 执行主函数
main "$@"
