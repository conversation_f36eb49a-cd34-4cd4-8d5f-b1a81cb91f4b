# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=True
SECRET_KEY=8895b5697b322900e3a2474c6723f1fa588570475e1131e6

# Database Configuration
DATABASE_URL=mysql+pymysql://footprint:footprint123@localhost:3306/footprint_subscription

# Redis Configuration (optional - comment out if Redis not installed)
# REDIS_URL=redis://localhost:6379/0

# WeChat OAuth Configuration
WECHAT_APPID=wxbd9c6afc23d98559
WECHAT_APPSECRET=c5a82f4ee1806acd62be144eb35b9314

# WeChat Pay Configuration
WXPAY_APPID=wxbd9c6afc23d98559
WXPAY_MCH_ID=1727996379
WXPAY_API_KEY=8wMuoF1k0F8hSEtM86WDg1jf5P2OrrNz
WXPAY_MCH_PRIVATE_KEY_PATH=./certs/apiclient_key.pem
WXPAY_CERT_SERIAL=7A70A0F2B47751DB4D29ECF93109A6995274FA40

WXPAY_PLATFORM_CERT_PATH=./certs/wechatpay_cert.pem

# Webhook Configuration
WEBHOOK_BASE_URL=http://localhost:8000

# JWT Configuration
JWT_SECRET_KEY=jwt-secret-key-for-development
JWT_ACCESS_TOKEN_EXPIRES=86400

# Logging
LOG_LEVEL=DEBUG

WXPAY_PUB_KEY_ID=PUB_KEY_ID_0117279963792025101500382058003400
WXPAY_PUB_CERT_PATH=./certs/pub_key.pem


# 开发环境
# 本地开发环境配置
FRONTEND_DOMAIN=localhost:5001
ADMIN_DOMAIN=localhost:8080
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:5001,http://127.0.0.1:8080,http://localhost:5001,http://127.0.0.1:5001
