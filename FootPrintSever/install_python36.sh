#!/bin/bash
"""
Python 3.6 环境安装脚本
适用于阿里云 CentOS/RHEL 系统
"""

set -e

echo "🚀 Python 3.6 环境 FootPrint 服务安装"
echo "适用于阿里云服务器"
echo "=" * 50

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    local message=$1
    local status=$2
    
    case $status in
        "ok")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# 检查 Python 版本
check_python() {
    print_status "检查 Python 环境..." "info"
    
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_status "当前 Python 版本: $PYTHON_VERSION" "info"
    
    if [[ "$PYTHON_VERSION" < "3.6" ]]; then
        print_status "Python 版本过低，需要 3.6+" "error"
        exit 1
    elif [[ "$PYTHON_VERSION" < "3.8" ]]; then
        print_status "Python 3.6/3.7 检测到，使用兼容配置" "warning"
        USE_COMPAT=true
    else
        print_status "Python 3.8+ 检测到，可以使用最新版本" "ok"
        USE_COMPAT=false
    fi
}

# 升级 pip
upgrade_pip() {
    print_status "升级 pip..." "info"
    python3 -m pip install --upgrade pip
    print_status "pip 升级完成" "ok"
}

# 安装依赖
install_dependencies() {
    print_status "安装项目依赖..." "info"
    
    if [ "$USE_COMPAT" = true ]; then
        print_status "使用 Python 3.6 兼容版本" "warning"
        pip3 install -r requirements_python36.txt
    else
        print_status "使用标准版本" "ok"
        pip3 install -r requirements.txt
    fi
    
    print_status "依赖安装完成" "ok"
}

# 检查安装结果
verify_installation() {
    print_status "验证安装..." "info"
    
    python3 -c "
import sys
print(f'Python: {sys.version}')

try:
    import flask
    print(f'✅ Flask: {flask.__version__}')
except ImportError as e:
    print(f'❌ Flask 导入失败: {e}')
    exit(1)

try:
    import flask_sqlalchemy
    print(f'✅ Flask-SQLAlchemy: {flask_sqlalchemy.__version__}')
except ImportError as e:
    print(f'❌ Flask-SQLAlchemy 导入失败: {e}')

try:
    import pymysql
    print('✅ PyMySQL: OK')
except ImportError as e:
    print(f'❌ PyMySQL 导入失败: {e}')

try:
    import redis
    print('✅ Redis: OK')
except ImportError as e:
    print(f'❌ Redis 导入失败: {e}')
"
    
    print_status "安装验证完成" "ok"
}

# 创建启动脚本
setup_startup() {
    print_status "设置启动脚本..." "info"
    
    chmod +x python36_start.py
    
    if [ "$USE_COMPAT" = true ]; then
        print_status "使用 python36_start.py 作为启动文件" "warning"
        STARTUP_FILE="python36_start.py"
    else
        print_status "可以使用 bt_start.py 或 python36_start.py" "ok"
        STARTUP_FILE="bt_start.py"
    fi
    
    print_status "启动脚本设置完成" "ok"
}

# 主函数
main() {
    print_status "开始安装..." "info"
    
    # 检查 Python 版本
    check_python
    
    # 升级 pip
    upgrade_pip
    
    # 安装依赖
    install_dependencies
    
    # 验证安装
    verify_installation
    
    # 设置启动脚本
    setup_startup
    
    echo
    print_status "安装完成！" "ok"
    echo
    print_status "使用方法:" "info"
    
    if [ "$USE_COMPAT" = true ]; then
        echo "  启动文件: python36_start.py"
        echo "  开发环境: python3 python36_start.py"
        echo "  生产环境: FLASK_ENV=production python3 python36_start.py"
        echo "  宝塔面板: 使用 python36_start.py 作为启动文件"
    else
        echo "  启动文件: bt_start.py 或 python36_start.py"
        echo "  开发环境: python3 bt_start.py"
        echo "  生产环境: FLASK_ENV=production python3 bt_start.py"
        echo "  宝塔面板: 使用 bt_start.py 作为启动文件"
    fi
    
    echo
    print_status "注意事项:" "warning"
    echo "  1. 确保 MySQL 数据库已创建"
    echo "  2. 配置 .env 文件中的数据库连接"
    echo "  3. 运行数据库迁移: flask db upgrade"
    echo "  4. Python 3.6 环境功能可能有限制"
}

# 执行主函数
main "$@"
