#!/usr/bin/env python3
"""
Python 3.6 兼容启动文件
适用于阿里云 Python 3.6.8 环境
"""
import os
import sys
from dotenv import load_dotenv

# 确保项目根目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 检查 Python 版本
if sys.version_info < (3, 6):
    print("❌ 需要 Python 3.6 或更高版本")
    sys.exit(1)

print(f"🐍 Python 版本: {sys.version}")

# 加载环境变量
load_dotenv()

# 只在开发环境加载 .env.local
flask_env = os.environ.get('FLASK_ENV', 'development')
if flask_env == 'development':
    load_dotenv('.env.local')

# 清除代理设置
for var in ['http_proxy', 'https_proxy', 'all_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY']:
    os.environ.pop(var, None)

try:
    # 导入应用
    from app import create_app
    from app.config import config
    
    # 创建应用实例
    app = create_app(config[flask_env])
    
    # 宝塔需要的应用变量
    application = app
    
    print(f"✅ 应用创建成功 (Flask {app.__class__.__module__})")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请检查依赖是否正确安装:")
    print("pip install -r requirements_python36.txt")
    sys.exit(1)

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    
    print(f"🚀 FootPrint 服务启动 (Python 3.6 兼容版)")
    print(f"📍 端口: {port}")
    print(f"🔧 环境: {flask_env}")
    print("=" * 50)
    
    # Python 3.6 环境建议使用 Flask 开发服务器
    if flask_env == 'production':
        try:
            import gunicorn.app.wsgiapp as wsgi
            print("✅ 使用 Gunicorn 生产服务器")
            
            # Python 3.6 兼容的 Gunicorn 配置
            sys.argv = [
                'gunicorn',
                '--bind', f'0.0.0.0:{port}',
                '--workers', '2',
                '--worker-class', 'sync',
                '--timeout', '120',
                '--keepalive', '5',
                '--max-requests', '1000',
                '--preload',
                'python36_start:app'
            ]
            wsgi.run()
        except ImportError:
            print("⚠️  Gunicorn 未安装，使用 Flask 开发服务器")
            print("⚠️  注意: 生产环境建议使用 Gunicorn")
            app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
    else:
        # 开发环境
        app.run(host='0.0.0.0', port=port, debug=True, threaded=True)
