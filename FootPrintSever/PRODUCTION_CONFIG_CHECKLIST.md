# 🚀 生产环境配置检查清单

## ✅ 已修复的问题

### 1. Flask 配置
- ✅ `FLASK_ENV=production` - 已设置为生产环境
- ✅ `FLASK_DEBUG=False` - 已关闭调试模式
- ✅ `SECRET_KEY` - 已使用强密钥

### 2. 安全配置
- ✅ `JWT_SECRET_KEY` - 已更新为生产密钥
- ✅ `REDIS_URL` - 已启用 Redis
- ✅ `WEBHOOK_BASE_URL` - 已改为 HTTPS
- ✅ `LOG_LEVEL=INFO` - 已设置合适的日志级别

### 3. CORS 配置
- ✅ `FRONTEND_DOMAIN` - 已移除端口号
- ✅ `ADMIN_DOMAIN` - 已设置子域名
- ✅ `ALLOWED_ORIGINS` - 已改为 HTTPS

## ⚠️ 仍需手动处理的问题

### 1. 数据库密码 (高优先级)
```bash
# 当前配置
DATABASE_URL=mysql+pymysql://footprint:CHANGE_TO_STRONG_PASSWORD@localhost:3306/footprint_subscription

# 建议修改为强密码，例如：
DATABASE_URL=mysql+pymysql://footprint:Fp2024!@#$StrongPass@localhost:3306/footprint_subscription
```

### 2. 微信支付密钥保护 (高优先级)
```bash
# 当前配置暴露了真实密钥，建议：
# 1. 使用环境变量注入
# 2. 使用密钥管理服务
# 3. 确保 .env 文件不被提交到版本控制

WECHAT_APPSECRET=c5a82f4ee1806acd62be144eb35b9314  # ⚠️ 真实密钥
WXPAY_API_KEY=8wMuoF1k0F8hSEtM86WDg1jf5P2OrrNz      # ⚠️ 真实密钥
```

### 3. 证书文件检查
```bash
# 确保以下证书文件存在且有效：
./certs/apiclient_key.pem      # 微信支付商户私钥
./certs/wechatpay_cert.pem     # 微信支付平台证书
./certs/pub_key.pem            # 公钥证书

# 检查证书权限：
chmod 600 ./certs/*.pem
```

### 4. 域名和 SSL 配置
```bash
# 确保以下域名已配置 SSL 证书：
- steplife.top
- admin.steplife.top
- www.steplife.top

# 检查 SSL 证书有效性：
curl -I https://steplife.top
curl -I https://admin.steplife.top
```

## 🔒 生产环境安全建议

### 1. 环境变量管理
```bash
# 不要在 .env 文件中存储敏感信息
# 使用系统环境变量或密钥管理服务

# 示例：使用系统环境变量
export WECHAT_APPSECRET="your-real-secret"
export WXPAY_API_KEY="your-real-api-key"
export DATABASE_PASSWORD="your-strong-password"
```

### 2. 文件权限设置
```bash
# 设置 .env 文件权限
chmod 600 .env

# 设置证书文件权限
chmod 600 certs/*.pem

# 设置日志目录权限
chmod 755 logs/
```

### 3. 防火墙配置
```bash
# 只开放必要端口
- 80 (HTTP，重定向到 HTTPS)
- 443 (HTTPS)
- 22 (SSH，限制 IP)

# 关闭不必要的端口
- 3306 (MySQL，仅内网访问)
- 6379 (Redis，仅内网访问)
- 5000 (应用端口，通过 Nginx 代理)
```

## 📋 部署前最终检查

### 1. 配置验证
```bash
# 运行配置检查脚本
python scripts/simple_check.py

# 检查环境变量
python fix_config.py
```

### 2. 服务测试
```bash
# 健康检查
curl https://steplife.top/healthz

# API 测试
curl https://steplife.top/api/health

# 管理后台测试
curl https://admin.steplife.top/admin/
```

### 3. 日志监控
```bash
# 检查日志文件
tail -f logs/footprint_subscription.log
tail -f logs/footprint_errors.log
```

## 🚨 紧急修复项

1. **立即修改数据库密码**
2. **保护微信支付密钥**
3. **配置 SSL 证书**
4. **设置文件权限**
5. **配置防火墙规则**

## 📞 部署后验证

1. ✅ 所有 API 接口正常响应
2. ✅ 微信登录功能正常
3. ✅ 微信支付功能正常
4. ✅ 管理后台可访问
5. ✅ HTTPS 证书有效
6. ✅ 日志正常记录
7. ✅ 数据库连接正常
8. ✅ Redis 缓存正常
