#!/usr/bin/env python3
"""
服务诊断脚本
检查服务运行状态和网络连接
"""
import os
import sys
import socket
import subprocess
import requests
from dotenv import load_dotenv

load_dotenv()

def check_port_binding(port):
    """检查端口绑定状态"""
    print(f"🔍 检查端口 {port} 绑定状态...")
    
    try:
        # 检查端口是否被占用
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        for line in lines:
            if f':{port}' in line and 'LISTEN' in line:
                print(f"✅ 端口 {port} 正在监听:")
                print(f"   {line.strip()}")
                return True
        
        print(f"❌ 端口 {port} 未在监听")
        return False
        
    except Exception as e:
        print(f"❌ 检查端口失败: {e}")
        return False

def check_socket_connection(host, port):
    """检查 socket 连接"""
    print(f"🔍 测试 socket 连接 {host}:{port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Socket 连接成功")
            return True
        else:
            print(f"❌ Socket 连接失败，错误码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Socket 测试失败: {e}")
        return False

def check_http_request(url):
    """检查 HTTP 请求"""
    print(f"🔍 测试 HTTP 请求: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ HTTP 请求成功")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:100]}...")
        return True
        
    except requests.exceptions.ConnectionError:
        print(f"❌ HTTP 连接被拒绝")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ HTTP 请求超时")
        return False
    except Exception as e:
        print(f"❌ HTTP 请求失败: {e}")
        return False

def check_process():
    """检查 Python 进程"""
    print("🔍 检查 Python 进程...")
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        python_processes = []
        for line in lines:
            if 'python' in line.lower() and ('bt_start' in line or 'FootPrint' in line or '8080' in line):
                python_processes.append(line.strip())
        
        if python_processes:
            print("✅ 找到相关 Python 进程:")
            for proc in python_processes:
                print(f"   {proc}")
            return True
        else:
            print("❌ 未找到相关 Python 进程")
            return False
            
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False

def check_firewall():
    """检查防火墙状态"""
    print("🔍 检查防火墙状态...")
    
    try:
        # 检查 iptables
        result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ iptables 规则:")
            print(result.stdout[:200] + "...")
        
        # 检查 ufw (如果存在)
        result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ UFW 状态:")
            print(result.stdout)
            
    except Exception as e:
        print(f"⚠️  防火墙检查失败: {e}")

def main():
    """主诊断函数"""
    print("🏥 FootPrint 服务诊断")
    print("=" * 50)
    
    # 获取配置
    port = int(os.environ.get('PORT', 8080))
    
    # 1. 检查端口绑定
    port_ok = check_port_binding(port)
    print()
    
    # 2. 检查进程
    process_ok = check_process()
    print()
    
    # 3. 检查 socket 连接
    socket_ok = check_socket_connection('127.0.0.1', port)
    print()
    
    # 4. 检查 HTTP 请求
    http_ok = check_http_request(f'http://127.0.0.1:{port}/healthz')
    print()
    
    # 5. 检查防火墙
    check_firewall()
    print()
    
    # 总结
    print("📋 诊断总结:")
    print(f"   端口绑定: {'✅' if port_ok else '❌'}")
    print(f"   进程运行: {'✅' if process_ok else '❌'}")
    print(f"   Socket连接: {'✅' if socket_ok else '❌'}")
    print(f"   HTTP请求: {'✅' if http_ok else '❌'}")
    
    if not any([port_ok, socket_ok, http_ok]):
        print("\n🚨 建议操作:")
        print("1. 检查服务是否真正启动")
        print("2. 检查端口是否被其他进程占用")
        print("3. 检查防火墙设置")
        print("4. 尝试重启服务")

if __name__ == '__main__':
    main()
