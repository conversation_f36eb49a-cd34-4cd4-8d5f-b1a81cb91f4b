#!/usr/bin/env python3
"""
简化测试启动文件
用于排查启动问题
"""
import os
import sys
import traceback

print("🔍 开始启动测试...")

try:
    # 1. 检查基本环境
    print(f"Python 版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"Python 路径: {sys.path[:3]}...")
    
    # 2. 加载环境变量
    print("\n📋 加载环境变量...")
    from dotenv import load_dotenv
    load_dotenv()
    
    flask_env = os.environ.get('FLASK_ENV', 'development')
    port = int(os.environ.get('PORT', 8080))
    print(f"FLASK_ENV: {flask_env}")
    print(f"PORT: {port}")
    
    # 3. 测试导入
    print("\n📦 测试关键模块导入...")
    import flask
    print(f"✅ Flask {flask.__version__}")
    
    import pymysql
    print(f"✅ PyMySQL {pymysql.__version__}")
    
    try:
        import redis
        print(f"✅ Redis {redis.__version__}")
    except ImportError:
        print("⚠️  Redis 未安装")
    
    # 4. 测试应用创建
    print("\n🏗️  测试应用创建...")
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    from app import create_app
    from app.config import config
    
    app = create_app(config[flask_env])
    print("✅ 应用创建成功")
    
    # 5. 测试数据库连接
    print("\n🗄️  测试数据库连接...")
    with app.app_context():
        from app.extensions import db
        try:
            # 简单的数据库连接测试
            db.engine.execute('SELECT 1')
            print("✅ 数据库连接成功")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
    
    # 6. 启动服务
    print(f"\n🚀 启动测试服务 (端口 {port})...")
    print("按 Ctrl+C 停止服务")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,
        use_reloader=False
    )
    
except KeyboardInterrupt:
    print("\n👋 服务已停止")
except Exception as e:
    print(f"\n❌ 启动失败:")
    print(f"错误: {e}")
    print("\n详细错误信息:")
    traceback.print_exc()
    sys.exit(1)
