# Python 3.6 兼容版本依赖
# 专为阿里云 Python 3.6.8 环境优化

# Flask 核心 (Python 3.6 最高支持版本)
Flask==2.0.3
Flask-SQLAlchemy==2.5.1
Flask-Migrate==3.1.0
Flask-JWT-Extended==4.4.4
Flask-CORS==3.0.10
flask-talisman==0.8.1
Flask-Limiter==2.1
Flask-APScheduler==1.12.4

# 数据库驱动
PyMySQL==1.0.2
cryptography==3.4.8

# HTTP 客户端 (Python 3.6 兼容版本)
httpx==0.23.3

# 微信支付 SDK (检查兼容性)
wechatpayv3==1.2.12

# Redis (Python 3.6 兼容版本)
redis==4.3.6

# 任务调度 (Python 3.6 兼容版本)
APScheduler==3.9.1

# 配置和环境
python-dotenv==0.19.2

# 日志 (Python 3.6 兼容版本)
structlog==21.5.0

# 时区支持
pytz==2022.7.1

# 开发和测试 (Python 3.6 兼容版本)
pytest==6.2.5
pytest-flask==1.2.0
pytest-mock==3.6.1

# 生产服务器 (Python 3.6 兼容版本)
gunicorn==20.1.0

# 其他依赖的兼容版本
Werkzeug==2.0.3
Jinja2==3.0.3
click==8.0.4
itsdangerous==2.0.1
MarkupSafe==2.0.1
